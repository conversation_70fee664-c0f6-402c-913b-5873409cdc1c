-- Optimized query to get case with all related data in single query
-- This eliminates N+1 problem by using JOINs instead of multiple separate queries
SELECT 
    c.id as case_id,
    c.case_name,
    c.description as case_description,
    c.status as case_status,
    c.created_at,
    c.qtn_id,
    c.lf_id,
    c.user_id,
    c.case_id_pms,
    c.created_by as assigned_by,
    
    -- Law firm info
    lf.lf_org_name,
    ui_owner.mb_phone as phone_number,
    
    g.id as group_id,
    g.gr_name as group_name,
    g.tooltips as group_tooltips,
    g.answer_id as group_answer_id,
    g.created_at as group_created_at,

    q.id as question_id,
    q.name as question_name,
    q.text as question_text,
    q.answer_type,
    q.required,
    q.tooltips as question_tooltips,
    q.selectAnswerTable,
    q.parent_answer,
    q.path as question_files,
    q.created_at as question_created_at,
    
    a.ans_id,
    a.name as answer_name,
    a.modal_id,
    a.modal_type,
    a.answer as answer_content,
    
    ans.id as answers_id,
    ans.name as answers_name,
    ans.text as answers_text
    
FROM cases c
LEFT JOIN law_firm lf ON lf.lf_id = c.lf_id
LEFT JOIN users u_owner ON u_owner.lf_id = c.lf_id AND u_owner.is_owner = 1
LEFT JOIN user_info ui_owner ON ui_owner.user_id = u_owner.user_id
LEFT JOIN questionaires qtn ON qtn.id = c.qtn_id
LEFT JOIN `groups` g ON g.tem_id = qtn.tem_id
LEFT JOIN questions q ON q.gr_id = g.id
LEFT JOIN answer a ON a.ques_id = q.id
LEFT JOIN answers ans ON ans.ques_id = a.ans_id
WHERE c.id = ?
ORDER BY g.gr_id, q.ques_id, a.id, ans.answer_id;
